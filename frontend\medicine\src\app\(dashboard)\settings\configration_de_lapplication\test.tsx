import React from 'react'

const test = () => {
  return (
 <>
 <div class="param-body mn-module flex layout-column md-whiteframe-z1 ng-scope" ui-view="" style=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <div class="mn-module-icon">
            <md-icon md-font-icon="mdi-heart-pulse" md-font-set="mdi" class="md-font mdi mdi-heart-pulse" role="img" aria-label="mdi-heart-pulse"></md-icon>
        </div>
        <h2 translate-once="general_oximetry_config">Oxymètrie</h2>
        <span flex="" class="flex"></span>
    </div>
</md-toolbar>

<md-content class="mn-module-body layout-column flex ng-scope _md">
    <md-tabs flex="" md-border-bottom="" class="ng-isolate-scope flex"><md-tabs-wrapper> <md-tab-data>
        <md-tab md-active="true" class="ng-scope ng-isolate-scope">
        </md-tab>
    </md-tab-data> 
    <md-tabs-canvas tabindex="0" ng-focus="$mdTabsCtrl.redirectFocus()" ng-class="{ 'md-paginated': $mdTabsCtrl.shouldPaginate, 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" ng-keydown="$mdTabsCtrl.keydown($event)"> <md-pagination-wrapper ng-class="{ 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" md-tab-scroll="$mdTabsCtrl.scroll($event)" role="tablist" aria-label="Use the left and right arrow keys to navigate between tabs" style="transform: translate(0px, 0px);"><!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-120" md-tab-id="120" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-120" style="">
    <span translate-once="general_oximetry_config_global" class="ng-scope">General</span>
            </md-tab-item>
            <md-tab-item tabindex="-1" class="md-tab" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-121" md-tab-id="121" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-121" style="">
                <span translate-once="general_oximetry_config_chart_colors" class="ng-scope">Couleur du chart</span>
            <div class="md-ripple-container" style=""></div></md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-122" md-tab-id="122" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-122" style="">
                <span translate-once="general_oximetry_config_protocols" class="ng-scope">Protocoles d'éxamen</span>
            <div class="md-ripple-container" style=""></div></md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="0" class="md-tab  md-active" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-123" md-tab-id="123" aria-selected="true" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-123" style="">
                <span translate-once="general_oximetry_config_reasons" class="ng-scope">Motifs d'éxamen</span>
            <div class="md-ripple-container" style=""></div></md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> <md-ink-bar style="left: 337px; right: -1px;" class=""></md-ink-bar> </md-pagination-wrapper> <md-tabs-dummy-wrapper aria-hidden="true" class="md-visually-hidden md-dummy-wrapper"> <!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_oximetry_config_global" class="ng-scope">General</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_oximetry_config_chart_colors" class="ng-scope">Couleur du chart</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_oximetry_config_protocols" class="ng-scope">Protocoles d'éxamen</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_oximetry_config_reasons" class="ng-scope">Motifs d'éxamen</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> </md-tabs-dummy-wrapper> </md-tabs-canvas> </md-tabs-wrapper> <md-tabs-content-wrapper ng-show="$mdTabsCtrl.hasContent &amp;&amp; $mdTabsCtrl.selectedIndex >= 0" class="_md" aria-hidden="false"> <!-- ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-120" class="_md ng-scope md-left" role="tabpanel" aria-labelledby="tab-item-120" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-121" class="_md ng-scope md-left" role="tabpanel" aria-labelledby="tab-item-121" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-122" class="_md ng-scope md-left" role="tabpanel" aria-labelledby="tab-item-122" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-123" class="_md ng-scope md-active" role="tabpanel" aria-labelledby="tab-item-123" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --><div md-tabs-template="::tab.template" md-connected-if="tab.isActive()" md-scope="::tab.parent" ng-if="$mdTabsCtrl.enableDisconnect || tab.shouldRender()" class="ng-scope ng-isolate-scope" style="">
                <md-content class="md-padding layout-column flex ng-scope _md" mn-bind-html="parameters/views/oxymetry-config-reasons.html" cg-busy="vm.promise"><div class="flex layout-column">
    <mn-system-parameter class="flex ng-isolate-scope" key="oximetry_config" attr="exam_motifs">
        <mn-oximetry-config-table mn-model="vm.model" mn-auto-save="vm.handleAutoSave" attr="exam_motifs" class="ng-isolate-scope"><!-- ngIf: vm.attr != 'chart_colors' --><div class="table-container flex ng-scope" ng-if="vm.attr != 'chart_colors'">
    <md-table-container style="margin: 0">
        <table md-table="" md-progress="vm.promise" ng-model="vm.model" class="ng-pristine ng-untouched ng-valid md-table ng-isolate-scope ng-empty" aria-invalid="false">
            <thead md-head="" md-order="id" class="md-head ng-isolate-scope">
            <tr md-row="" class="md-row">
                <!-- ngIf: vm.attr== 'exam_motifs' --><th md-column="" ng-if="vm.attr== 'exam_motifs'" class="md-column ng-scope ng-isolate-scope">
                    <span translate-once="oximetry_exam_type">Type d'examen</span>
                </th><!-- end ngIf: vm.attr== 'exam_motifs' -->
                <!-- ngIf: vm.attr== 'exam_motifs' --><th md-column="" ng-if="vm.attr== 'exam_motifs'" class="md-column ng-scope ng-isolate-scope">
                    <span translate-once="oximetry_motif">Motif</span>
                </th><!-- end ngIf: vm.attr== 'exam_motifs' -->
                <!-- ngIf: vm.attr== 'protocols' -->
                <!-- ngIf: vm.attr== 'protocols' -->
                <!-- ngIf: vm.attr== 'protocols' -->
                <th md-column="" class="actions-column-1 md-column ng-isolate-scope"></th>
            </tr>
            </thead>
            <thead class="md-table-progress ng-isolate-scope" md-table-progress=""><tr>
  <th colspan="3">
    <md-progress-linear ng-show="deferred()" md-mode="indeterminate" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" class="ng-hide"><div class="md-container md-mode-indeterminate"><div class="md-dashed"></div><div class="md-bar md-bar1"></div><div class="md-bar md-bar2"></div></div></md-progress-linear>
  </th>
</tr></thead><!-- ngIf: !vm.model || vm.model.length== 0 --><tbody md-body="" ng-if="!vm.model || vm.model.length== 0" class="md-body ng-scope">
            <tr md-row="" class="md-row">
                <td md-cell="" colspan="6" class="md-cell">
                    <span translate-once="no_element_to_show">Aucun élément trouvé.</span>
                </td>
                <td md-cell="" class="hidden md-cell"></td>
            </tr>
            </tbody><!-- end ngIf: !vm.model || vm.model.length== 0 -->
            <tbody md-body="" class="md-body">
            <!-- ngRepeat: line in vm.model | limitTo:  vm.query.limit: (vm.query.page - 1) * vm.query.limit track by $index -->
            </tbody>
        </table>
    </md-table-container>
    <md-table-pagination class="with-borders md-table-pagination ng-isolate-scope" md-boundary-links="" md-limit-options="::vm.options" md-label="{&quot;page&quot;:&quot;Page&quot;,&quot;rowsPerPage&quot;:&quot;Lignes par Page&quot;,&quot;of&quot;:&quot;de&quot;}" md-limit="vm.query.limit" md-page="vm.query.page" md-total="" md-on-paginate="vm.onPaginate" md-page-select=""><!-- ngIf: $pagination.showPageSelect() --><div class="page-select ng-scope" ng-if="$pagination.showPageSelect()">
  <div class="label ng-binding">Page</div>

  <md-select virtual-page-select="" total="1" class="md-table-select ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" ng-model="$pagination.page" md-container-class="md-pagination-select" ng-change="$pagination.onPaginationChange()" ng-disabled="$pagination.disabled" aria-label="Page" tabindex="0" role="button" aria-haspopup="listbox" id="select_159" aria-invalid="false" aria-labelledby="select_159 select_value_label_158"><md-select-value class="md-select-value" id="select_value_label_158"><span><div class="md-text ng-binding">1</div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container md-pagination-select" aria-hidden="true" role="presentation" id="select_container_160">  <md-select-menu role="presentation" class="_md">
    <md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_161">
      <!-- ngRepeat: page in $pageSelect.pages --><md-option ng-repeat="page in $pageSelect.pages" ng-value="page" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_166" value="1" selected="selected" aria-selected="true"><div class="md-text ng-binding">1</div></md-option><!-- end ngRepeat: page in $pageSelect.pages -->
    </md-content>
  </md-select-menu></div></md-select>
</div><!-- end ngIf: $pagination.showPageSelect() -->

<!-- ngIf: $pagination.limitOptions --><div class="limit-select ng-scope" ng-if="$pagination.limitOptions">
  <div class="label ng-binding">Lignes par Page</div>

  <md-select class="md-table-select ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" ng-model="$pagination.limit" md-container-class="md-pagination-select" ng-disabled="$pagination.disabled" aria-label="Rows" placeholder="1" tabindex="0" role="button" aria-haspopup="listbox" id="select_163" aria-invalid="false" aria-labelledby="select_163 select_value_label_162"><md-select-value class="md-select-value" id="select_value_label_162"><span><div class="md-text ng-binding">2</div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container md-pagination-select" aria-hidden="true" role="presentation" id="select_container_164">  <md-select-menu role="presentation" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_165">
    <!-- ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_167" value="1"><div class="md-text ng-binding">1</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_168" value="2" selected="selected" aria-selected="true"><div class="md-text ng-binding">2</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_169" value="4"><div class="md-text ng-binding">4</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_170" value="10"><div class="md-text ng-binding">10</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions -->
  </md-content></md-select-menu></div></md-select>
</div><!-- end ngIf: $pagination.limitOptions -->

<div class="buttons">
  <div class="label ng-binding">0 -  de </div>

  <!-- ngIf: $pagination.showBoundaryLinks() --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="$pagination.showBoundaryLinks()" ng-click="$pagination.first()" ng-disabled="$pagination.disabled || !$pagination.hasPrevious()" aria-label="First" disabled="disabled">
    <md-icon md-svg-icon="navigate-first.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M7 6 v12 h2 v-12 h-2z M17.41 7.41L16 6l-6 6 6 6 1.41-1.41L12.83 12z"></path></svg></md-icon>
  </button><!-- end ngIf: $pagination.showBoundaryLinks() -->

  <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="$pagination.previous()" ng-disabled="$pagination.disabled || !$pagination.hasPrevious()" aria-label="Previous" disabled="disabled">
    <md-icon md-svg-icon="navigate-before.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path></svg></md-icon>
  </button>

  <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="$pagination.next()" ng-disabled="$pagination.disabled || !$pagination.hasNext()" aria-label="Next" disabled="disabled">
    <md-icon md-svg-icon="navigate-next.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg></md-icon>
  </button>

  <!-- ngIf: $pagination.showBoundaryLinks() --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="$pagination.showBoundaryLinks()" ng-click="$pagination.last()" ng-disabled="$pagination.disabled || !$pagination.hasNext()" aria-label="Last" disabled="disabled">
    <md-icon md-svg-icon="navigate-last.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M15 6 v12 h2 v-12 h-2z M8 6L6.59 7.41 11.17 12l-4.58 4.59L8 18l6-6z"></path></svg></md-icon>
  </button><!-- end ngIf: $pagination.showBoundaryLinks() -->
</div></md-table-pagination>

</div><!-- end ngIf: vm.attr != 'chart_colors' -->


<!-- ngIf: vm.attr== 'chart_colors' -->




</mn-oximetry-config-table>
    </mn-system-parameter>
</div><div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"></div><div class="cg-busy ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-content>
            </div><!-- end ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --> </md-tabs-content-wrapper></md-tabs>
</md-content></div>
 
 </>
  )
}

export default test
