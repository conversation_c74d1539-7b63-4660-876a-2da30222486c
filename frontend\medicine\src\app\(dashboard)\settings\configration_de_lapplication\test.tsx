import React from 'react'

const test = () => {
  return (
 <>
<div class="param-body mn-module flex layout-column md-whiteframe-z1 ng-scope" ui-view="" style=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <div class="mn-module-icon">
            <md-icon md-font-icon="mdi-badge-account" md-font-set="mdi" class="md-font mdi mdi-badge-account" role="img" aria-label="mdi-badge-account"></md-icon>
        </div>
        <h2 translate-once="technical_file.setup">Fiche technique Supplémentaire</h2>
        <span flex="" class="flex"></span>
        <!-- ngIf: vm.currentList && vm.selectedTab === 'lists' -->
    </div>
</md-toolbar>

<md-content class="mn-module-body layout-fill flex layout-column medical-care-setup-container ng-scope _md">
    <md-tabs flex="" md-border-bottom="" md-selected="vm.selectedTabIndex" class="ng-isolate-scope flex"><md-tabs-wrapper> <md-tab-data>
        <md-tab md-on-select="vm.selectTab('general')" ng-disabled="true" class="ng-scope ng-isolate-scope" aria-disabled="true" disabled="disabled">
            
            
        </md-tab>

        <md-tab md-on-select="vm.selectTab('fields')" md-active="true" class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        <md-tab md-on-select="vm.selectTab('lists')" class="ng-scope ng-isolate-scope">
            
            
        </md-tab>
    </md-tab-data> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <md-tabs-canvas tabindex="0" ng-focus="$mdTabsCtrl.redirectFocus()" ng-class="{ 'md-paginated': $mdTabsCtrl.shouldPaginate, 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" ng-keydown="$mdTabsCtrl.keydown($event)"> <md-pagination-wrapper ng-class="{ 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" md-tab-scroll="$mdTabsCtrl.scroll($event)" role="tablist" aria-label="Use the left and right arrow keys to navigate between tabs" style="transform: translate(0px, 0px);"><!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab  md-disabled" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-626" md-tab-id="626" aria-selected="false" aria-disabled="true" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" disabled="disabled" aria-controls="tab-content-626">
                <span translate-once="technical_file.general_config" class="ng-scope">Général</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-627" md-tab-id="627" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-627" style="">
                <span translate-once="technical_file.fields" class="ng-scope">Champs</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="0" class="md-tab  md-active" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-628" md-tab-id="628" aria-selected="true" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-628" style="">
                <span translate-once="technical_file.lists" class="ng-scope">Listes de choix</span>
            <div class="md-ripple-container" style=""></div></md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> <md-ink-bar class="" style="left: 136px; right: 0px;"></md-ink-bar> </md-pagination-wrapper> <md-tabs-dummy-wrapper aria-hidden="true" class="md-visually-hidden md-dummy-wrapper"> <!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="technical_file.general_config" class="ng-scope">Général</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="technical_file.fields" class="ng-scope">Champs</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="technical_file.lists" class="ng-scope">Listes de choix</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> </md-tabs-dummy-wrapper> </md-tabs-canvas> </md-tabs-wrapper> <md-tabs-content-wrapper ng-show="$mdTabsCtrl.hasContent &amp;&amp; $mdTabsCtrl.selectedIndex >= 0" class="_md" aria-hidden="false"> <!-- ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-626" class="_md ng-scope md-left" role="tabpanel" aria-labelledby="tab-item-626" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }"> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-627" class="_md ng-scope md-left" role="tabpanel" aria-labelledby="tab-item-627" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-628" class="_md ng-scope md-active" role="tabpanel" aria-labelledby="tab-item-628" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --><div md-tabs-template="::tab.template" md-connected-if="tab.isActive()" md-scope="::tab.parent" ng-if="$mdTabsCtrl.enableDisconnect || tab.shouldRender()" class="ng-scope ng-isolate-scope" style="">
                <div mn-bind-html="stand-alone/technical-file/controllers/technical-file-setup/technical-file-lists.html" class="layout md-padding ng-scope"><div flex="" layout="row" class="layout-row flex">
    <md-sidenav class="mn-module-side-nav md-locked-open md-sidenav-left layout-column flex-nogrow md-closed ng-isolate-scope _md" md-is-locked-open="true" tabindex="-1">
        <md-toolbar class="mn-module-header md-primary _md _md-toolbar-transitions">
            <div class="md-toolbar-tools">
                <div class="mn-module-icon">
                    <md-icon md-font-icon="mdi-format-list-bulleted" md-font-set="mdi" class="md-font mdi mdi-format-list-bulleted" role="img" aria-label="mdi-format-list-bulleted"></md-icon>
                </div>
                <h2>
                    <span translate-once="list_setup_models">Types</span>
                </h2>
                <span flex="" class="flex"></span>
            </div>
        </md-toolbar>
        <md-content flex="" layout="column" class="_md layout-column flex">
            <!-- ngIf: vm.lists --><md-list flex="" ng-if="vm.lists" role="list" class="ng-scope flex">
                <!-- ngRepeat: item in vm.lists track by item.slug -->
            </md-list><!-- end ngIf: vm.lists -->
        </md-content>
    </md-sidenav>
    <md-content class="mn-module-side-content layout-column flex _md">
        <div class="table-container flex">
            <md-table-container>
                <table md-table="" class="mn-striped md-table ng-isolate-scope" md-progress="vm.promise">
                    <thead md-head="" class="md-head ng-isolate-scope">
                    <tr md-row="" class="md-row">
                        <th md-column="" class="md-column ng-isolate-scope">
                            <span translate-once="list_setup_value">Nom</span>
                        </th>
                        <th md-column="" class="md-column ng-isolate-scope">
                            <span translate-once="list_setup_description">Description</span>
                        </th>
                        <th md-column="" class="md-column ng-isolate-scope">
                            <span translate-once="list_setup_is_hidden">Caché</span>
                        </th>
                        <th md-column="" class="actions-column-3 right-aligned md-column ng-isolate-scope">
                            <!-- ngIf: vm.selectedListItems && vm.selectedListItems.length > 0 -->
                            <!-- ngIf: vm.selectedListItems && vm.selectedListItems.length > 0 -->
                        </th>
                    </tr>
                    </thead>
                    <thead class="md-table-progress ng-isolate-scope" md-table-progress=""><tr>
  <th colspan="4">
    <md-progress-linear ng-show="deferred()" md-mode="indeterminate" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" class="ng-hide"><div class="md-container md-mode-indeterminate"><div class="md-dashed"></div><div class="md-bar md-bar1"></div><div class="md-bar md-bar2"></div></div></md-progress-linear>
  </th>
</tr></thead><tbody md-body="" dragula="&quot;item-bag&quot;" dragula-model="vm.selectedListItems" class="md-body ng-isolate-scope">

                    <!-- ngIf: !vm.selectedListItems || vm.selectedListItems.length == 0 --><tr md-row="" ng-if="!vm.selectedListItems || vm.selectedListItems.length == 0" class="md-row ng-scope">
                        <td md-cell="" colspan="4" translate-once="no_element_to_show" class="md-cell">Aucun élément trouvé.</td>
                        <td md-cell="" ng-hide="true" class="md-cell ng-hide" aria-hidden="true"></td>
                    </tr><!-- end ngIf: !vm.selectedListItems || vm.selectedListItems.length == 0 -->

                    <!-- ngRepeat: item in vm.selectedListItems -->
                    </tbody>
                </table>
            </md-table-container>
        </div>
    </md-content>
</div></div>
            </div><!-- end ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --> </md-tabs-content-wrapper></md-tabs>
</md-content>
</div>
 
 </>
  )
}

export default test
